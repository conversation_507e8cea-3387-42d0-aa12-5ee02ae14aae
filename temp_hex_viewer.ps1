$content = Get-Content 'project\DTSD1980-hc32l196\Debug\Exe\app.bin' -Encoding Byte
$offset = 0x5000

if ($offset -lt $content.Length) {
    Write-Host "Viewing binary content at offset 0x$($offset.ToString('X8')):"
    Write-Host ""
    
    for ($i = 0; $i -lt 128 -and ($offset + $i) -lt $content.Length; $i += 16) {
        $line = ""
        $ascii = ""
        
        for ($j = 0; $j -lt 16 -and ($offset + $i + $j) -lt $content.Length; $j++) {
            $byte = $content[$offset + $i + $j]
            $line += "{0:X2} " -f $byte
            
            if ($byte -ge 32 -and $byte -le 126) {
                $ascii += [char]$byte
            } else {
                $ascii += "."
            }
        }
        
        $address = $offset + $i
        Write-Host ("{0:X8}: {1,-48} {2}" -f $address, $line.TrimEnd(), $ascii)
    }
} else {
    Write-Host "Offset 0x$($offset.ToString('X8')) is beyond file size ($($content.Length) bytes)"
}
