/**
 ******************************************************************************
* @file    intvec_tab.c
* <AUTHOR> @date    2024
* @brief   中断重定向表
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*       /// HC32L196系列中断向量表
        ; External Interrupts
            DCD     PORTA_IRQHandler
            DCD     PORTB_IRQHandler
            DCD     PORTC_E_IRQHandler
            DCD     PORTD_F_IRQHandler
            DCD     DMAC_IRQHandler
            DCD     TIM3_IRQHandler
            DCD     UART0_2_IRQHandler
            DCD     UART1_3_IRQHandler
            DCD     LPUART0_IRQHandler
            DCD     LPUART1_IRQHandler
            DCD     SPI0_IRQHandler
            DCD     SPI1_IRQHandler
            DCD     I2C0_IRQHandler
            DCD     I2C1_IRQHandler
            DCD     TIM0_IRQHandler
            DCD     TIM1_IRQHandler
            DCD     TIM2_IRQHandler
            DCD     LPTIM0_1_IRQHandler
            DCD     TIM4_IRQHandler
            DCD     TIM5_IRQHandler
            DCD     TIM6_IRQHandler
            DCD     PCA_IRQHandler
            DCD     WDT_IRQHandler
            DCD     RTC_IRQHandler
            DCD     ADC_DAC_IRQHandler
            DCD     PCNT_IRQHandler
            DCD     VC0_IRQHandler
            DCD     VC1_2_IRQHandler
            DCD     LVD_IRQHandler
            DCD     LCD_IRQHandler
            DCD     FLASH_RAM_IRQHandler
            DCD     CLKTRIM_IRQHandler

******************************************************************************/


#ifndef __ICCARM__
    #define __weak __attribute__((weak))
#endif

#pragma section = ".intvec"
/* Includes ------------------------------------------------------------------*/
#include "intvec_tab.h"


extern void systick_handler(void);


#ifndef NULL
#define NULL   (void*)0
#endif

#pragma location = ".boot_ram"
static void(*(int_vector_tab[INT_NUM]))(); // 中断请求向量

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
__weak void NMI_Handler(void)
{
    while (1){}
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
__weak void HardFault_Handler(void)
{
    while (1){}
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
__weak void SVC_Handler(void)
{
	while (1){}
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
__weak void PendSV_Handler(void)
{
	while (1){}
}

// /**
//   * @brief  This function handles SysTick Handler.
//   * @param  None
//   * @retval None
//   */
// __weak void SysTick_Handler(void)
// {
//     systick_handler();
// }


// 宏定义用于定义中断处理函数的声明
#define _PRAGMA(x)  _Pragma(#x)
#define IRQ_FUN_DECLARATION(v,f) \
void f(void) \
{ \
	if(int_vector_tab[v] != NULL) int_vector_tab[v](); \
}
// 宏定义用于定义中断处理函数的定义
IRQ_FUN_DECLARATION(INT_PORTA,      PORTA_IRQHandler)
IRQ_FUN_DECLARATION(INT_PORTB,      PORTB_IRQHandler)
IRQ_FUN_DECLARATION(INT_PORTC_E,    PORTC_E_IRQHandler)
IRQ_FUN_DECLARATION(INT_PORTD_F,    PORTD_F_IRQHandler)
IRQ_FUN_DECLARATION(INT_DMAC,       DMAC_IRQHandler)
IRQ_FUN_DECLARATION(INT_TIM3,       TIM3_IRQHandler)
IRQ_FUN_DECLARATION(INT_UART0_2,    UART0_2_IRQHandler)
IRQ_FUN_DECLARATION(INT_UART1_3,    UART1_3_IRQHandler)
IRQ_FUN_DECLARATION(INT_LPUART0,    LPUART0_IRQHandler)
IRQ_FUN_DECLARATION(INT_LPUART1,    LPUART1_IRQHandler)
IRQ_FUN_DECLARATION(INT_SPI0,       SPI0_IRQHandler)
IRQ_FUN_DECLARATION(INT_SPI1,       SPI1_IRQHandler)
IRQ_FUN_DECLARATION(INT_I2C0,       I2C0_IRQHandler)
IRQ_FUN_DECLARATION(INT_I2C1,       I2C1_IRQHandler)
IRQ_FUN_DECLARATION(INT_TIM0,       TIM0_IRQHandler)
IRQ_FUN_DECLARATION(INT_TIM1,       TIM1_IRQHandler)    
IRQ_FUN_DECLARATION(INT_TIM2,       TIM2_IRQHandler)    
IRQ_FUN_DECLARATION(INT_LPTIM0_1,   LPTIM0_1_IRQHandler)    
IRQ_FUN_DECLARATION(INT_TIM4,       TIM4_IRQHandler)    
IRQ_FUN_DECLARATION(INT_TIM5,       TIM5_IRQHandler)    
IRQ_FUN_DECLARATION(INT_TIM6,       TIM6_IRQHandler)    
IRQ_FUN_DECLARATION(INT_PCA,        PCA_IRQHandler)    
IRQ_FUN_DECLARATION(INT_WDT,        WDT_IRQHandler)    
IRQ_FUN_DECLARATION(INT_RTC,        RTC_IRQHandler)    
IRQ_FUN_DECLARATION(INT_ADC_DAC,    ADC_DAC_IRQHandler)
IRQ_FUN_DECLARATION(INT_PCNT,       PCNT_IRQHandler)
IRQ_FUN_DECLARATION(INT_VC0,        VC0_IRQHandler)
IRQ_FUN_DECLARATION(INT_VC1_2,      VC1_2_IRQHandler)
IRQ_FUN_DECLARATION(INT_LVD,        LVD_IRQHandler)
IRQ_FUN_DECLARATION(INT_LCD,        LCD_IRQHandler)
IRQ_FUN_DECLARATION(INT_FLASH_RAM,  FLASH_RAM_IRQHandler)
IRQ_FUN_DECLARATION(INT_CLKTRIM,    CLKTRIM_IRQHandler)


/// 配置中断向量调用的处理函数.
/// @param  irq-中断类型
/// @param  vec-用户处理函数(或者清中断标志函数)指针.
/// @note   本函数允许设置空函数指针NULL用于取消中断调用处理函数.但对于必须清零
///         中断标志的中断向量，必须设置包含清中断标志的函数指针。
void int_vector_set(int irq, void vec(void))
{
    if(int_vector_tab[irq] != vec) int_vector_tab[irq] = vec;
}


/** @} */
/** @} */
/** @} */
